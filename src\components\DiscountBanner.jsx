import React from 'react';
import { motion } from 'framer-motion';
import { Tag, Gift, Sparkles } from 'lucide-react';
import { useDiscounts } from '../contexts/DiscountContext';
import { formatDiscountDescription } from '../utils/discountUtils';

const DiscountBanner = ({ productId = null, className = "" }) => {
  const { discounts, isLoading } = useDiscounts();

  if (isLoading) return null;

  // If productId is provided, show only discounts for that product
  const relevantDiscounts = productId 
    ? discounts.filter(discount => 
        discount.active && 
        discount.applicableProducts && 
        discount.applicableProducts.includes(productId)
      )
    : discounts.filter(discount => discount.active);

  if (relevantDiscounts.length === 0) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {relevantDiscounts.map((discount, index) => (
        <motion.div
          key={discount.id}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-lg shadow-md"
        >
          <div className="flex items-center justify-center space-x-2">
            <Gift size={18} className="animate-pulse" />
            <span className="font-semibold text-sm sm:text-base">
              🎉 {formatDiscountDescription(discount)}
            </span>
            <Sparkles size={18} className="animate-pulse" />
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Compact version for product cards
export const DiscountBadge = ({ productId, className = "" }) => {
  const { discounts } = useDiscounts();

  const productDiscounts = discounts.filter(discount => 
    discount.active && 
    discount.applicableProducts && 
    discount.applicableProducts.includes(productId)
  );

  if (productDiscounts.length === 0) return null;

  return (
    <div className={`absolute top-2 right-2 z-10 ${className}`}>
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg"
      >
        <div className="flex items-center space-x-1">
          <Tag size={12} />
          <span>OFFER</span>
        </div>
      </motion.div>
    </div>
  );
};

// Banner for cart page showing potential savings
export const CartDiscountBanner = ({ cartItems, getItemPrice }) => {
  const { discounts } = useDiscounts();
  
  if (!cartItems || cartItems.length === 0) return null;

  // Check for potential discounts
  const potentialDiscounts = [];
  
  for (const discount of discounts) {
    if (!discount.active) continue;
    
    const applicableItems = cartItems.filter(item => 
      discount.applicableProducts.includes(item.product.id)
    );
    
    if (applicableItems.length === 0) continue;
    
    const totalQuantity = applicableItems.reduce((sum, item) => sum + item.quantity, 0);
    
    if (discount.type === 'buyXgetY') {
      const needed = discount.buyQuantity - totalQuantity;
      if (needed > 0) {
        potentialDiscounts.push({
          discount,
          itemsNeeded: needed,
          description: formatDiscountDescription(discount)
        });
      }
    }
  }

  if (potentialDiscounts.length === 0) return null;

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div className="flex items-start space-x-3">
        <Gift className="text-yellow-600 mt-1" size={20} />
        <div>
          <h3 className="font-semibold text-yellow-800 mb-2">
            🎁 You're close to a great deal!
          </h3>
          {potentialDiscounts.map((item, index) => (
            <p key={index} className="text-yellow-700 text-sm">
              Add {item.itemsNeeded} more eligible item{item.itemsNeeded > 1 ? 's' : ''} to get: <strong>{item.description}</strong>
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DiscountBanner;
